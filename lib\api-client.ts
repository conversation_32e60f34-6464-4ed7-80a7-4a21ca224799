import { supabase } from "./supabase";
import { debugLog } from "./env";

interface ApiRequestOptions {
	method?: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
	body?: any;
	headers?: Record<string, string>;
}

class ApiClient {
	private baseUrl: string;

	constructor() {
		this.baseUrl =
			process.env.NODE_ENV === "development"
				? typeof window !== "undefined"
					? window.location.origin
					: "http://localhost:3001"
				: window.location.origin;
	}

	private async getAuthHeaders(): Promise<Record<string, string>> {
		const {
			data: { session },
		} = await supabase.auth.getSession();

		const headers: Record<string, string> = {
			"Content-Type": "application/json",
		};

		if (session?.access_token) {
			headers["Authorization"] = `Bearer ${session.access_token}`;
		}

		return headers;
	}

	async request<T = any>(endpoint: string, options: ApiRequestOptions = {}): Promise<T> {
		const { method = "GET", body, headers: customHeaders = {} } = options;

		const authHeaders = await this.getAuthHeaders();
		const headers = { ...authHeaders, ...customHeaders };

		const config: RequestInit = {
			method,
			headers,
		};

		if (body && method !== "GET") {
			config.body = JSON.stringify(body);
		}

		const url = endpoint.startsWith("http") ? endpoint : `${this.baseUrl}${endpoint}`;

		try {
			const response = await fetch(url, config);

			if (!response.ok) {
				const errorData = await response.json().catch(() => ({ error: "Unknown error" }));
				throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
			}

			// Handle empty responses
			const contentType = response.headers.get("content-type");
			if (contentType && contentType.includes("application/json")) {
				return await response.json();
			}

			return response.text() as any;
		} catch (error) {
			console.error("API request failed:", error);
			throw error;
		}
	}

	// Convenience methods
	async get<T = any>(endpoint: string, headers?: Record<string, string>): Promise<T> {
		return this.request<T>(endpoint, { method: "GET", headers });
	}

	async post<T = any>(endpoint: string, body?: any, headers?: Record<string, string>): Promise<T> {
		return this.request<T>(endpoint, { method: "POST", body, headers });
	}

	async put<T = any>(endpoint: string, body?: any, headers?: Record<string, string>): Promise<T> {
		return this.request<T>(endpoint, { method: "PUT", body, headers });
	}

	async patch<T = any>(endpoint: string, body?: any, headers?: Record<string, string>): Promise<T> {
		return this.request<T>(endpoint, { method: "PATCH", body, headers });
	}

	async delete<T = any>(endpoint: string, body?: any, headers?: Record<string, string>): Promise<T> {
		return this.request<T>(endpoint, { method: "DELETE", body, headers });
	}
}

// Create singleton instance
export const apiClient = new ApiClient();

// Admin API specific methods
export const adminApi = {
	// Services
	getServices: (params?: Record<string, any>) => {
		const query = params ? `?${new URLSearchParams(params).toString()}` : "";
		return apiClient.get(`/api/admin/services${query}`);
	},

	getService: (id: string) => apiClient.get(`/api/admin/services/${id}`),

	createService: (data: any) => apiClient.post("/api/admin/services", data),

	updateService: (id: string, data: any) => apiClient.put(`/api/admin/services/${id}`, data),

	deleteService: (id: string) => apiClient.delete(`/api/admin/services/${id}`),

	// Service Scheduling Rules
	getServiceSchedulingRules: (serviceId: string) =>
		apiClient.get(`/api/admin/services/${serviceId}/scheduling-rules`),

	createServiceSchedulingRule: (serviceId: string, data: any) =>
		apiClient.post(`/api/admin/services/${serviceId}/scheduling-rules`, data),

	updateServiceSchedulingRule: (serviceId: string, ruleId: string, data: any) =>
		apiClient.put(`/api/admin/services/${serviceId}/scheduling-rules/${ruleId}`, data),

	deleteServiceSchedulingRule: (serviceId: string, ruleId: string) =>
		apiClient.delete(`/api/admin/services/${serviceId}/scheduling-rules/${ruleId}`),

	// Service Blackout Dates
	getServiceBlackoutDates: (serviceId: string) => apiClient.get(`/api/admin/services/${serviceId}/blackout-dates`),

	createServiceBlackoutDate: (serviceId: string, data: any) =>
		apiClient.post(`/api/admin/services/${serviceId}/blackout-dates`, data),

	updateServiceBlackoutDate: (serviceId: string, blackoutId: string, data: any) =>
		apiClient.put(`/api/admin/services/${serviceId}/blackout-dates/${blackoutId}`, data),

	deleteServiceBlackoutDate: (serviceId: string, blackoutId: string) =>
		apiClient.delete(`/api/admin/services/${serviceId}/blackout-dates/${blackoutId}`),

	// Reservations
	getReservations: (params?: Record<string, any>) => {
		const query = params ? `?${new URLSearchParams(params).toString()}` : "";
		return apiClient.get(`/api/admin/reservations${query}`);
	},

	getReservation: (id: string) => apiClient.get(`/api/admin/reservations/${id}`),

	createReservation: (data: any) => apiClient.post("/api/admin/reservations/create", data),

	updateReservation: (id: string, data: any) => apiClient.put(`/api/admin/reservations/${id}`, data),

	cancelReservation: (id: string, data?: any) => apiClient.delete(`/api/admin/reservations/${id}`, data),

	// Customers
	getCustomers: (params?: Record<string, any>) => {
		const query = params ? `?${new URLSearchParams(params).toString()}` : "";
		return apiClient.get(`/api/admin/customers${query}`);
	},

	getCustomer: (id: string) => apiClient.get(`/api/admin/customers/${id}`),

	createCustomer: (data: any) => apiClient.post("/api/admin/customers", data),

	updateCustomer: (id: string, data: any) => apiClient.put(`/api/admin/customers/${id}`, data),

	// Employees
	getEmployees: (params?: Record<string, any>) => {
		const query = params ? `?${new URLSearchParams(params).toString()}` : "";
		return apiClient.get(`/api/admin/employees${query}`);
	},

	getEmployee: (id: string) => apiClient.get(`/api/admin/employees/${id}`),

	createEmployee: (data: any) => apiClient.post("/api/admin/employees", data),

	updateEmployee: (id: string, data: any) => apiClient.put(`/api/admin/employees/${id}`, data),

	deleteEmployee: (id: string) => apiClient.delete(`/api/admin/employees/${id}`),

	// Users
	getUsers: (params?: Record<string, any>) => {
		const query = params ? `?${new URLSearchParams(params).toString()}` : "";
		return apiClient.get(`/api/admin/users${query}`);
	},

	getUser: (id: string) => apiClient.get(`/api/admin/users/${id}`),

	createUser: (data: any) => apiClient.post("/api/admin/users", data),

	updateUser: (id: string, data: any) => apiClient.put(`/api/admin/users/${id}`, data),

	deleteUser: (id: string) => apiClient.delete(`/api/admin/users/${id}`),

	// Employee Service Qualifications
	getEmployeeServiceQualifications: (params?: Record<string, any>) => {
		const query = params ? `?${new URLSearchParams(params).toString()}` : "";
		return apiClient.get(`/api/admin/employee-service-qualifications${query}`);
	},

	createEmployeeServiceQualification: (data: any) =>
		apiClient.post("/api/admin/employee-service-qualifications", data),

	updateEmployeeServiceQualification: (id: string, data: any) =>
		apiClient.put(`/api/admin/employee-service-qualifications/${id}`, data),

	deleteEmployeeServiceQualification: (id: string) =>
		apiClient.delete(`/api/admin/employee-service-qualifications/${id}`),

	// Service Equipment Requirements
	createServiceEquipmentRequirement: (data: any) => apiClient.post("/api/admin/service-equipment-requirements", data),

	updateServiceEquipmentRequirement: (id: string, data: any) =>
		apiClient.put(`/api/admin/service-equipment-requirements/${id}`, data),

	deleteServiceEquipmentRequirement: (id: string) =>
		apiClient.delete(`/api/admin/service-equipment-requirements/${id}`),

	// Notifications
	getNotifications: (params?: Record<string, any>) => {
		const query = params ? `?${new URLSearchParams(params).toString()}` : "";
		return apiClient.get(`/api/admin/notifications${query}`);
	},

	getNotification: (id: string) => apiClient.get(`/api/admin/notifications/${id}`),

	createNotification: (data: any) => apiClient.post("/api/admin/notifications", data),

	updateNotification: (id: string, data: any) => apiClient.patch(`/api/admin/notifications/${id}`, data),

	deleteNotification: (id: string) => apiClient.delete(`/api/admin/notifications/${id}`),

	markNotificationsAsRead: (notificationIds: string[]) =>
		apiClient.patch("/api/admin/notifications", {
			action: "mark_read",
			notification_ids: notificationIds,
		}),

	// Equipment
	getEquipment: (params?: Record<string, any>) => {
		const query = params ? `?${new URLSearchParams(params).toString()}` : "";
		return apiClient.get(`/api/admin/equipment${query}`);
	},

	getEquipmentItem: (id: string) => apiClient.get(`/api/admin/equipment/${id}`),

	createEquipment: (data: any) => apiClient.post("/api/admin/equipment", data),

	updateEquipment: (id: string, data: any) => apiClient.put(`/api/admin/equipment/${id}`, data),

	deleteEquipment: (id: string) => apiClient.delete(`/api/admin/equipment/${id}`),

	// Pricing Tiers
	getPricingTiers: (params?: Record<string, any>) => {
		const query = params ? `?${new URLSearchParams(params).toString()}` : "";
		return apiClient.get(`/api/admin/pricing-tiers${query}`);
	},

	getPricingTier: (id: string) => apiClient.get(`/api/admin/pricing-tiers/${id}`),

	createPricingTier: (data: any) => apiClient.post("/api/admin/pricing-tiers", data),

	updatePricingTier: (id: string, data: any) => apiClient.put(`/api/admin/pricing-tiers/${id}`, data),

	deletePricingTier: (id: string) => apiClient.delete(`/api/admin/pricing-tiers/${id}`),

	// Discount Coupons
	getDiscountCoupons: (params?: Record<string, any>) => {
		const query = params ? `?${new URLSearchParams(params).toString()}` : "";
		return apiClient.get(`/api/admin/discount-coupons${query}`);
	},

	getDiscountCoupon: (id: string) => apiClient.get(`/api/admin/discount-coupons/${id}`),

	createDiscountCoupon: (data: any) => apiClient.post("/api/admin/discount-coupons", data),

	updateDiscountCoupon: (id: string, data: any) => apiClient.put(`/api/admin/discount-coupons/${id}`, data),

	deleteDiscountCoupon: (id: string) => apiClient.delete(`/api/admin/discount-coupons/${id}`),

	// Customer Feedback
	getCustomerFeedback: (params?: Record<string, any>) => {
		const query = params ? `?${new URLSearchParams(params).toString()}` : "";
		return apiClient.get(`/api/admin/customer-feedback${query}`);
	},

	getFeedback: (id: string) => apiClient.get(`/api/admin/customer-feedback/${id}`),

	updateFeedback: (id: string, data: any) => apiClient.put(`/api/admin/customer-feedback/${id}`, data),

	respondToFeedback: (id: string, data: any) => apiClient.post(`/api/admin/customer-feedback/${id}/respond`, data),

	// Analytics
	getAnalytics: (params?: Record<string, any>) => {
		const query = params ? `?${new URLSearchParams(params).toString()}` : "";
		return apiClient.get(`/api/admin/analytics${query}`);
	},

	populateAnalytics: (data?: any) => apiClient.post("/api/admin/analytics/populate", data),

	// Settings
	getSettings: (params?: Record<string, any>) => {
		const query = params ? `?${new URLSearchParams(params).toString()}` : "";
		return apiClient.get(`/api/admin/settings${query}`);
	},

	updateSettings: (data: Record<string, any>) => apiClient.put("/api/admin/settings", data),

	createSetting: (data: Record<string, any>) => apiClient.post("/api/admin/settings", data),

	// Service Options
	getServiceOptions: () => apiClient.get("/api/admin/service-options"),

	createServiceOption: (data: any) => apiClient.post("/api/admin/service-options", data),

	updateServiceOption: (data: any) => apiClient.put("/api/admin/service-options", data),

	deleteServiceOption: (id: string) => apiClient.delete(`/api/admin/service-options?id=${id}`),

	// Service Option Groups removed - simplified to individual options only

	// Service Option Assignments
	getServiceOptionAssignments: (serviceId: string) =>
		apiClient.get(`/api/admin/services/${serviceId}/option-assignments`),

	updateServiceOptionAssignments: (serviceId: string, assignments: any) =>
		apiClient.put(`/api/admin/services/${serviceId}/option-assignments`, { assignments }),

	deleteServiceOptionAssignments: (serviceId: string) =>
		apiClient.delete(`/api/admin/services/${serviceId}/option-assignments`),

	// Service Required Groups
	getServiceRequiredGroups: (serviceId: string) => apiClient.get(`/api/admin/services/${serviceId}/required-groups`),

	updateServiceRequiredGroups: (serviceId: string, requiredGroups: any) =>
		apiClient.put(`/api/admin/services/${serviceId}/required-groups`, { requiredGroups }),

	deleteServiceRequiredGroups: (serviceId: string) =>
		apiClient.delete(`/api/admin/services/${serviceId}/required-groups`),

	// Auth
	verifyAuth: () => apiClient.get("/api/auth/admin/verify"),
};

export default apiClient;
