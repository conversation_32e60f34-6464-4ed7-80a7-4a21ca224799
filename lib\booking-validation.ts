import { calculateEquipmentCapacity, getAvailableTimeSlots } from "./availability";
import { supabase } from "./supabase";
import { BookingFormData, ParticipantInfo, PriceCalculation, TierParticipantCount } from "./types";
import { formatTimeFromISO } from "./time-utils";

interface DiscountValidationResult {
	valid: boolean;
	discountAmount?: number;
	error?: string;
}

export interface ValidationResult {
	isValid: boolean;
	errors: string[];
	warnings: string[];
}

export interface BookingValidationResult extends ValidationResult {
	priceCalculation?: PriceCalculation;
}

/**
 * Validate participant information (simplified - only age validation)
 */
export function validateParticipant(participant: ParticipantInfo): ValidationResult {
	const errors: string[] = [];
	const warnings: string[] = [];

	// Age validation
	if (participant.age === undefined || participant.age === null || participant.age < 0 || participant.age > 120) {
		errors.push("L'âge doit être entre 0 et 120 ans");
	}

	// Age-specific warnings
	if (participant.age && participant.age < 3) {
		warnings.push("Les enfants de moins de 3 ans nécessitent une attention particulière");
	}
	if (participant.age && participant.age > 75) {
		warnings.push("Veuillez vous assurer que cette activité convient aux seniors");
	}

	return {
		isValid: errors.length === 0,
		errors,
		warnings,
	};
}

/**
 * Validate customer information
 */
export function validateCustomerInfo(customerInfo: any): ValidationResult {
	const errors: string[] = [];
	const warnings: string[] = [];

	// Required fields
	if (!customerInfo.firstName?.trim()) {
		errors.push("Le prénom du client est requis");
	}
	if (!customerInfo.lastName?.trim()) {
		errors.push("Le nom du client est requis");
	}
	if (!customerInfo.email?.trim()) {
		errors.push("L'email est requis");
	}
	if (!customerInfo.phone?.trim()) {
		errors.push("Le téléphone est requis");
	}

	// Email validation
	if (customerInfo.email) {
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		if (!emailRegex.test(customerInfo.email)) {
			errors.push("Format d'email invalide");
		}
	}

	// Phone validation (French format)
	if (customerInfo.phone) {
		const phoneRegex = /^(?:\+33|0)[1-9](?:[0-9]{8})$/;
		if (!phoneRegex.test(customerInfo.phone.replace(/\s/g, ""))) {
			warnings.push("Format de téléphone non standard (attendu: format français)");
		}
	}

	// Emergency contact validation
	if (customerInfo.emergencyContactName && !customerInfo.emergencyContactPhone) {
		warnings.push("Numéro de contact d'urgence manquant");
	}
	if (customerInfo.emergencyContactPhone && !customerInfo.emergencyContactName) {
		warnings.push("Nom du contact d'urgence manquant");
	}

	return {
		isValid: errors.length === 0,
		errors,
		warnings,
	};
}

/**
 * Validate a discount coupon server-side
 */
async function validateDiscountCoupon(
	code: string,
	serviceId: string,
	totalAmount: number
): Promise<DiscountValidationResult> {
	try {
		// Get the discount coupon
		const { data: coupon, error: couponError } = await supabase
			.from("discount_coupons")
			.select("*")
			.eq("code", code.toUpperCase())
			.eq("is_active", true)
			.single();

		if (couponError || !coupon) {
			return {
				valid: false,
				error: "Code de réduction invalide ou expiré",
			};
		}

		// Check if coupon is within valid date range
		const now = new Date();
		if (coupon.valid_from && new Date(coupon.valid_from) > now) {
			return {
				valid: false,
				error: "Ce code de réduction n'est pas encore valide",
			};
		}

		if (coupon.valid_until && new Date(coupon.valid_until) < now) {
			return {
				valid: false,
				error: "Ce code de réduction a expiré",
			};
		}

		// Check usage limit
		if (coupon.usage_limit && coupon.current_usage >= coupon.usage_limit) {
			return {
				valid: false,
				error: "Ce code de réduction a atteint sa limite d'utilisation",
			};
		}

		// Check if coupon applies to the specific service
		if (serviceId && coupon.applicable_services && coupon.applicable_services.length > 0) {
			if (!coupon.applicable_services.includes(serviceId)) {
				return {
					valid: false,
					error: "Ce code de réduction ne s'applique pas à ce service",
				};
			}
		}

		// Check minimum purchase amount
		if (coupon.min_purchase_amount && totalAmount < coupon.min_purchase_amount) {
			return {
				valid: false,
				error: `Montant minimum requis: ${coupon.min_purchase_amount}€`,
			};
		}

		// Calculate discount amount
		let discountAmount = 0;
		if (coupon.discount_type === "percentage") {
			discountAmount = (totalAmount * coupon.discount_value) / 100;
		} else if (coupon.discount_type === "fixed") {
			discountAmount = coupon.discount_value;
		}

		// Apply maximum discount limit if set
		if (coupon.max_discount_amount && discountAmount > coupon.max_discount_amount) {
			discountAmount = coupon.max_discount_amount;
		}

		// Ensure discount doesn't exceed total amount
		if (discountAmount > totalAmount) {
			discountAmount = totalAmount;
		}

		return {
			valid: true,
			discountAmount,
		};
	} catch (error) {
		console.error("Error validating discount coupon:", error);
		return {
			valid: false,
			error: "Erreur lors de la validation du code de réduction",
		};
	}
}

/**
 * Calculate pricing for participants using tier participant counts
 */
export async function calculateBookingPrice(
	serviceId: string,
	tierParticipants: TierParticipantCount[],
	selectedOptions?: string[] | import("./types/service-options").OptionSelection[],
	discountCouponCode?: string
): Promise<PriceCalculation> {
	try {
		const participantCount = tierParticipants.reduce((sum, tp) => sum + tp.count, 0);

		// Get service base price, fixed_price flag, and pricing tiers
		const { data: service, error: serviceError } = await supabase
			.from("services")
			.select("base_price, fixed_price")
			.eq("id", serviceId)
			.single();

		if (serviceError) throw serviceError;

		const { data: pricingTiers, error: tiersError } = await supabase
			.from("pricing_tiers")
			.select("*")
			.eq("service_id", serviceId)
			.eq("is_active", true)
			.order("min_age");

		if (tiersError) throw tiersError;

		// Handle fixed pricing vs per-participant pricing
		let subtotal: number;
		let pricePerParticipant: number;
		let participantPricing: any[];

		if (service.fixed_price) {
			// Fixed pricing: total price doesn't change with participant count
			subtotal = service.base_price;
			pricePerParticipant = service.base_price; // For display purposes

			// Create participant pricing array with fixed total distributed
			participantPricing = Array.from({ length: participantCount }, (_, index) => ({
				participant: { age: 25 }, // Default age for simplified booking
				tier: {
					id: "fixed",
					tier_name: "Prix fixe",
					price: index === 0 ? service.base_price : 0, // Only first participant shows the price
					min_age: 0,
					max_age: null,
					service_id: serviceId,
					is_active: true,
					created_at: new Date().toISOString(),
					updated_at: new Date().toISOString(),
				},
				price: index === 0 ? service.base_price : 0, // Only first participant shows the price
			}));
		} else {
			// Per-participant pricing: use tier participant counts
			participantPricing = [];
			subtotal = 0;

			for (const tierParticipant of tierParticipants) {
				// Find the pricing tier
				const tier = pricingTiers?.find((t) => t.id === tierParticipant.tierId);
				if (tier) {
					// Add entries for each participant in this tier
					for (let i = 0; i < tierParticipant.count; i++) {
						participantPricing.push({
							participant: { age: tier.min_age || 25 },
							tier,
							price: tier.price,
						});
					}
					subtotal += tier.price * tierParticipant.count;
				}
			}

			// If no tier participants provided, use fallback
			if (tierParticipants.length === 0 && participantCount > 0) {
				const defaultTier =
					pricingTiers && pricingTiers.length > 0
						? pricingTiers[0] // Use first tier as fallback
						: {
								id: "default",
								tier_name: "Standard",
								price: service.base_price,
								min_age: 0,
								max_age: null,
								service_id: serviceId,
								is_active: true,
								created_at: new Date().toISOString(),
								updated_at: new Date().toISOString(),
							};

				participantPricing = Array.from({ length: participantCount }, () => ({
					participant: { age: 25 },
					tier: defaultTier,
					price: defaultTier.price,
				}));

				subtotal = participantCount * defaultTier.price;
			}
		}

		// Calculate options price
		let optionsPrice = 0;
		if (selectedOptions && selectedOptions.length > 0) {
			// Check if we're using the new service options system
			const { data: serviceOptionsAssignments } = await supabase.rpc("get_service_options_with_groups", {
				service_uuid: serviceId,
			});

			if (serviceOptionsAssignments && serviceOptionsAssignments.length > 0) {
				// Use new service options system
				const { getServiceOptions, calculateOptionsPricing } = await import("./service-options");
				const assignments = await getServiceOptions(supabase, serviceId);

				// Convert string[] to OptionSelection[] if needed
				let optionSelections: import("./types/service-options").OptionSelection[];
				if (typeof selectedOptions[0] === "string") {
					optionSelections = (selectedOptions as string[]).map((optionId) => ({
						optionId,
						quantity: 1,
					}));
				} else {
					optionSelections = selectedOptions as import("./types/service-options").OptionSelection[];
				}

				const pricing = calculateOptionsPricing(assignments, optionSelections, participantCount);
				optionsPrice = pricing.total;
			} else {
				// Fallback to legacy options system
				const { data: serviceWithOptions } = await supabase
					.from("services")
					.select("options")
					.eq("id", serviceId)
					.single();

				if (serviceWithOptions?.options) {
					const serviceOptions = Array.isArray(serviceWithOptions.options) ? serviceWithOptions.options : [];
					const optionIds =
						typeof selectedOptions[0] === "string"
							? (selectedOptions as string[])
							: (selectedOptions as import("./types/service-options").OptionSelection[]).map(
									(s) => s.optionId
								);

					optionsPrice = serviceOptions
						.filter((option) => optionIds.includes(option.id))
						.reduce((sum, option) => {
							// Handle per-participant pricing for options
							const optionPrice = option.per_participant
								? (option.price || 0) * participantCount
								: option.price || 0;
							return sum + optionPrice;
						}, 0);
				}
			}
		}

		const totalWithOptions = subtotal + optionsPrice;

		// Apply discount coupon if provided
		let discountAmount = 0;
		if (discountCouponCode) {
			try {
				const discountValidation = await validateDiscountCoupon(
					discountCouponCode,
					serviceId,
					totalWithOptions
				);
				if (discountValidation.valid) {
					discountAmount = discountValidation.discountAmount || 0;
				}
			} catch (error) {
				console.error("Error validating discount coupon:", error);
				// Continue without discount if validation fails
			}
		}

		const finalTotal = Math.max(0, totalWithOptions - discountAmount);

		return {
			participants: participantPricing,
			subtotal,
			discountAmount,
			total: finalTotal,
		};
	} catch (error) {
		console.error("Error calculating booking price:", error);
		throw error;
	}
}

/**
 * Validate complete booking data
 */
export async function validateBooking(bookingData: BookingFormData): Promise<BookingValidationResult> {
	const errors: string[] = [];
	const warnings: string[] = [];

	try {
		// Validate service exists and is active
		console.log("=== BASIC VALIDATION DEBUG START ===");
		console.log("Validating service ID:", bookingData.serviceId);

		const { data: service, error: serviceError } = await supabase
			.from("services")
			.select("id, name, is_active, min_age, max_age, max_participants")
			.eq("id", bookingData.serviceId)
			.single();

		console.log("Service query result:", { service, serviceError });

		if (serviceError || !service) {
			console.log("Service validation failed:", serviceError);
			errors.push("Service non trouvé");
			return { isValid: false, errors, warnings };
		}

		if (!service.is_active) {
			console.log("Service is not active");
			errors.push("Ce service n'est plus disponible");
			return { isValid: false, errors, warnings };
		}

		console.log("Service validation passed");

		console.log("Starting time slot validation...");
		// Validate time slot using dynamic availability
		// Handle both formats: serviceId|date|time (public) and date_time (admin)
		let date: string;
		let time: string;

		if (bookingData.timeSlotId.includes("|")) {
			// Public booking format: serviceId|date|time
			const timeSlotParts = bookingData.timeSlotId.split("|");
			if (timeSlotParts.length < 3) {
				errors.push("Format de créneau horaire invalide");
				return { isValid: false, errors, warnings };
			}
			date = timeSlotParts[1];
			time = timeSlotParts[2];
		} else if (bookingData.timeSlotId.includes("_")) {
			// Admin booking format: date_time
			const timeSlotParts = bookingData.timeSlotId.split("_");
			if (timeSlotParts.length !== 2) {
				errors.push("Format de créneau horaire invalide");
				return { isValid: false, errors, warnings };
			}
			date = timeSlotParts[0];
			time = timeSlotParts[1];
		} else {
			errors.push("Format de créneau horaire invalide");
			return { isValid: false, errors, warnings };
		}

		// Get available time slots for this date
		const participantCount = bookingData.tierParticipants?.reduce((sum, tp) => sum + tp.count, 0) || 0;
		const availableSlots = await getAvailableTimeSlots(bookingData.serviceId, date, participantCount);

		// Find the specific time slot
		const timeSlot = availableSlots.find((slot) => {
			const slotTime = formatTimeFromISO(slot.start_time);
			console.log("Comparing slot time (timezone-safe):", slotTime, "with requested time:", time);
			return slotTime === time;
		});

		if (!timeSlot || !timeSlot.is_available) {
			errors.push("Créneau horaire non disponible");
			return { isValid: false, errors, warnings };
		}

		// Validate participant count
		if (participantCount === 0) {
			errors.push("Au moins un participant est requis");
			return { isValid: false, errors, warnings };
		}

		if (participantCount > service.max_participants) {
			errors.push(`Nombre maximum de participants dépassé (${service.max_participants})`);
		}

		// For simple bookings, we don't validate individual participant details
		// Just validate the count and basic service restrictions
		console.log(`Validating ${participantCount} participants for service ${service.name}`);

		// If service has age restrictions, add warnings but don't block booking
		if (service.min_age && service.min_age > 0) {
			warnings.push(`Âge minimum recommandé: ${service.min_age} ans`);
		}
		if (service.max_age) {
			warnings.push(`Âge maximum recommandé: ${service.max_age} ans`);
		}

		console.log("Starting customer validation...");
		// Validate customer info
		const customerValidation = validateCustomerInfo(bookingData.customerInfo);
		console.log("Customer validation result:", customerValidation);
		if (!customerValidation.isValid) {
			errors.push(...customerValidation.errors);
		}
		warnings.push(...customerValidation.warnings);
		console.log("Customer validation completed");

		console.log("Starting equipment capacity check...");
		console.log("TimeSlot object:", timeSlot);
		// Check equipment capacity
		try {
			const capacityCheck = await calculateEquipmentCapacity(
				bookingData.serviceId,
				timeSlot.start_time,
				timeSlot.end_time,
				participantCount
			);
			console.log("Equipment capacity check result:", capacityCheck);

			if (!capacityCheck.available) {
				errors.push("Capacité insuffisante pour ce créneau");
			}
			console.log("Equipment capacity check completed");
		} catch (capacityError) {
			console.error("Equipment capacity check failed:", capacityError);
			errors.push("Erreur lors de la vérification de la capacité");
		}

		// Calculate pricing
		let priceCalculation: PriceCalculation | undefined;
		if (errors.length === 0) {
			try {
				priceCalculation = await calculateBookingPrice(
					bookingData.serviceId,
					bookingData.tierParticipants || []
				);
			} catch (priceError) {
				errors.push("Erreur lors du calcul du prix");
				console.error("Price calculation error:", priceError);
			}
		}

		return {
			isValid: errors.length === 0,
			errors,
			warnings,
			priceCalculation,
		};
	} catch (error) {
		console.error("Error validating booking:", error);
		console.error("Error stack:", error instanceof Error ? error.stack : error);
		return {
			isValid: false,
			errors: [
				`Erreur lors de la validation de la réservation: ${
					error instanceof Error ? error.message : String(error)
				}`,
			],
			warnings,
		};
	}
}

/**
 * Validate booking before creation (final check)
 */
export async function validateBookingForCreation(bookingData: BookingFormData): Promise<ValidationResult> {
	console.log("=== VALIDATION FOR CREATION DEBUG START ===");
	const participantCount = bookingData.tierParticipants?.reduce((sum, tp) => sum + tp.count, 0) || 0;
	console.log("Validating booking data for creation:", {
		serviceId: bookingData.serviceId,
		timeSlotId: bookingData.timeSlotId,
		participantCount,
		tierParticipants: bookingData.tierParticipants,
		customerInfo: bookingData.customerInfo,
	});

	console.log("Running basic validation...");
	const validation = await validateBooking(bookingData);
	console.log("Basic validation result:", {
		isValid: validation.isValid,
		errors: validation.errors,
		warnings: validation.warnings,
	});

	// Additional checks for creation
	const errors = [...validation.errors];
	const warnings = [...validation.warnings];

	// Check if time slot is still available using dynamic availability (race condition protection)
	console.log("Checking time slot availability...");

	// Handle both timeSlotId formats
	let date: string;
	let time: string;
	let validFormat = false;

	if (bookingData.timeSlotId.includes("|")) {
		// Public booking format: serviceId|date|time
		const timeSlotParts = bookingData.timeSlotId.split("|");
		console.log("TimeSlot parts (public format):", timeSlotParts);
		if (timeSlotParts.length >= 3) {
			date = timeSlotParts[1];
			time = timeSlotParts[2];
			validFormat = true;
		}
	} else if (bookingData.timeSlotId.includes("_")) {
		// Admin booking format: date_time
		const timeSlotParts = bookingData.timeSlotId.split("_");
		console.log("TimeSlot parts (admin format):", timeSlotParts);
		if (timeSlotParts.length === 2) {
			date = timeSlotParts[0];
			time = timeSlotParts[1];
			validFormat = true;
		}
	}

	if (validFormat) {
		console.log("Extracted date:", date, "time:", time);

		try {
			console.log("Fetching available slots for:", {
				serviceId: bookingData.serviceId,
				date,
				participantCount,
			});

			const availableSlots = await getAvailableTimeSlots(bookingData.serviceId, date, participantCount);

			console.log("Available slots found:", availableSlots.length);
			console.log(
				"Available slots:",
				availableSlots.map((slot) => ({
					start_time: slot.start_time,
					is_available: slot.is_available,
					available_capacity: slot.available_capacity,
				}))
			);

			const timeSlot = availableSlots.find((slot) => {
				const slotTime = formatTimeFromISO(slot.start_time);
				console.log("Comparing slot time (timezone-safe):", slotTime, "with requested time:", time);
				return slotTime === time;
			});

			console.log("Found matching time slot:", timeSlot ? "YES" : "NO");

			if (!timeSlot || !timeSlot.is_available) {
				console.log("Time slot validation failed:", {
					timeSlotFound: !!timeSlot,
					isAvailable: timeSlot?.is_available,
				});
				errors.push("Ce créneau n'est plus disponible");
			} else {
				console.log("Time slot validation passed");
			}
		} catch (error) {
			console.error("Error checking time slot availability:", error);
			errors.push("Erreur lors de la vérification de disponibilité");
		}
	} else {
		console.log("Invalid timeSlot format:", bookingData.timeSlotId);
		errors.push("Format de créneau horaire invalide");
	}

	console.log("Final validation result:", {
		isValid: errors.length === 0,
		errors,
		warnings,
	});
	console.log("=== VALIDATION FOR CREATION DEBUG END ===");

	return {
		isValid: errors.length === 0,
		errors,
		warnings,
	};
}
